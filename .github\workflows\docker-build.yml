name: Multi-Service Docker Build and Push

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  BASE_IMAGE_NAME: pxpat-backend

jobs:
  # 检测变更的服务
  detect-changes:
    name: Detect Service Changes
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.changes.outputs.services }}
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Detect changes
      id: changes
      run: |
        # 定义服务配置
        services='[
          {
            "name": "content-management-service",
            "dockerfile": "docker/services/content-management/Dockerfile",
            "context": ".",
            "path": "internal/content-cluster/content-management-service",
            "cluster": "content-cluster"
          }
        ]'

        # 检查是否有服务变更
        changed_services='[]'

        if [ "${{ github.event_name }}" == "push" ] && [ "${{ github.ref }}" == "refs/heads/main" ]; then
          # 主分支推送时构建所有服务
          changed_services="$services"
        else
          # 检查具体变更的服务
          for service in $(echo "$services" | jq -r '.[] | @base64'); do
            service_data=$(echo "$service" | base64 --decode)
            service_name=$(echo "$service_data" | jq -r '.name')
            service_path=$(echo "$service_data" | jq -r '.path')
            dockerfile=$(echo "$service_data" | jq -r '.dockerfile')

            # 检查服务相关文件是否有变更
            if git diff --name-only HEAD~1 HEAD | grep -E "^($service_path/|$dockerfile|go\.mod|go\.sum|pkg/)" > /dev/null; then
              changed_services=$(echo "$changed_services" | jq ". += [$service_data]")
            fi
          done
        fi

        echo "services=$(echo "$changed_services" | jq -c '.')" >> $GITHUB_OUTPUT

    - name: Set matrix
      id: set-matrix
      run: |
        services='${{ steps.changes.outputs.services }}'
        if [ "$services" == "[]" ]; then
          echo "matrix={\"include\":[]}" >> $GITHUB_OUTPUT
        else
          echo "matrix={\"include\":$(echo "$services" | jq -c '.')}" >> $GITHUB_OUTPUT
        fi

  # 构建变更的服务镜像
  build-services:
    name: Build Service Images
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.services != '[]'
    strategy:
      matrix: ${{ fromJson(needs.detect-changes.outputs.matrix) }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.BASE_IMAGE_NAME }}/${{ matrix.name }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
        labels: |
          org.opencontainers.image.title=${{ matrix.name }}
          org.opencontainers.image.description=PXPAT Backend ${{ matrix.name }}
          org.opencontainers.image.vendor=PXPAT Team
          org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
          org.opencontainers.image.revision=${{ github.sha }}
          org.opencontainers.image.created=${{ github.event.head_commit.timestamp }}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ${{ matrix.context }}
        file: ${{ matrix.dockerfile }}
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha,scope=${{ matrix.name }}
        cache-to: type=gha,mode=max,scope=${{ matrix.name }}
        platforms: linux/amd64,linux/arm64
        build-args: |
          BUILDTIME=${{ github.event.head_commit.timestamp }}
          VERSION=${{ github.ref_name }}
          REVISION=${{ github.sha }}
          SERVICE_NAME=${{ matrix.name }}
          CLUSTER_NAME=${{ matrix.cluster }}

    - name: Run Trivy vulnerability scanner
      if: github.event_name != 'pull_request'
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.BASE_IMAGE_NAME }}/${{ matrix.name }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results-${{ matrix.name }}.sarif'

    - name: Upload Trivy scan results
      if: github.event_name != 'pull_request'
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results-${{ matrix.name }}.sarif'
        category: 'trivy-${{ matrix.name }}'

  # 多架构构建测试
  multi-arch-test:
    name: Multi-Architecture Build Test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Test multi-arch build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/Dockerfile.content-management
        platforms: linux/amd64,linux/arm64
        push: false
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 镜像安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-matrix
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Run Snyk to check Docker image for vulnerabilities
      uses: snyk/actions/docker@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        image: ${{ env.REGISTRY }}/${{ env.BASE_IMAGE_NAME }}/content-management-service:${{ github.sha }}
        args: --severity-threshold=high
    
    - name: Upload Snyk results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: snyk.sarif

  # 清理旧镜像
  cleanup:
    name: Cleanup Old Images
    runs-on: ubuntu-latest
    needs: build-matrix
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Delete old images
      uses: actions/delete-package-versions@v4
      with:
        package-name: 'pxpat-backend/content-management-service'
        package-type: 'container'
        min-versions-to-keep: 10
        delete-only-untagged-versions: true

  # 部署通知
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [build-matrix, security-scan]
    if: always() && github.event_name == 'push'
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        custom_payload: |
          {
            attachments: [{
              color: '${{ job.status }}' === 'success' ? 'good' : '${{ job.status }}' === 'failure' ? 'danger' : 'warning',
              blocks: [{
                type: 'section',
                text: {
                  type: 'mrkdwn',
                  text: `Docker build for *${{ github.repository }}* \n*${{ job.status }}*: ${{ github.event.head_commit.message }}`
                }
              }]
            }]
          }
