# Redis 缓存配置
version: '3.8'

services:
  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
      - ../../configs/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - database-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    sysctls:
      - net.core.somaxconn=1024

# 网络配置
networks:
  database-network:
    driver: bridge
    external: true

# 数据卷配置
volumes:
  redis_data:
    driver: local
